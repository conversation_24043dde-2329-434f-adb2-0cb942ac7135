import { Link } from 'react-router-dom'
import Logo from './Logo'

export default function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-5 gap-8">
          {/* Mission Fresh Brand */}
          <div>
            <div className="mb-4">
              <Logo size="md" showText={true} linkTo="/" className="text-primary-foreground hover:opacity-90" />
            </div>
            <p className="text-primary-foreground text-sm max-w-xs">
              A personalized journey to a smoke-free life, powered by AI and holistic wellness principles.
            </p>
          </div>

          {/* Resources Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">RESOURCES</h3>
            <nav className="space-y-2">
              <Link to="/tools/nrt-guide" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                NRT Guide
              </Link>
              <Link to="/tools/smokeless-directory" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Smokeless Directory
              </Link>
              <Link to="/tools/quit-methods" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Quit Methods
              </Link>
              <Link to="/tools/calculators" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Calculators
              </Link>
            </nav>
          </div>

          {/* App Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">APP</h3>
            <nav className="space-y-2">
              <Link to="/features" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Features
              </Link>
              <Link to="/dashboard" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Dashboard
              </Link>
              <Link to="/progress" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Progress
              </Link>
              <Link to="/community" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Community
              </Link>
            </nav>
          </div>

          {/* Company Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">COMPANY</h3>
            <nav className="space-y-2">
              <Link to="/about" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                About
              </Link>
              <Link to="/privacy-policy" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Privacy Policy
              </Link>
              <Link to="/terms-of-service" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Terms of Service
              </Link>
              <Link to="/how-it-works" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                How it Works
              </Link>
            </nav>
          </div>

          {/* Support Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4">SUPPORT</h3>
            <nav className="space-y-2">
              <Link to="/help-center" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Help Center
              </Link>
              <Link to="/contact-us" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Contact Us
              </Link>
              <Link to="/faq" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                FAQ
              </Link>
              <Link to="/feedback" className="block text-primary-foreground hover:opacity-90 text-sm transition-colors">
                Feedback
              </Link>
            </nav>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-border mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-primary-foreground text-sm mb-4 md:mb-0">
              © 2025 Mission Fresh. All Rights Reserved.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-primary-foreground hover:opacity-90">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-primary-foreground hover:opacity-90">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="#" className="text-primary-foreground hover:opacity-90">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
