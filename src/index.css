/*
  ======================================================================================
  MISSION FRESH - COLORS AND FONTS ONLY - HOLY RULE 0003 COMPLIANCE
  Apple-style elegance - Only ONE shade per color across entire app
  NO sizing, spacing, or layout properties allowed
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Border Radius - Apple Style Elegance */
    --radius: 0.75rem;

    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Primary Brand Colors - ONLY ONE GREEN ALLOWED */
    --primary: 160 84.2% 39.4%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary Colors - ONLY ONE SHADE */
    --secondary: 0 0% 100%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 0 0% 96%;
    
    /* Muted Colors */
    --muted: 0 0% 98%;
    --muted-foreground: 215.4 16.3% 35%;
    --muted-subtle: 0 0% 98%;
    
    /* Accent Colors - ONLY ONE SHADE */
    --accent: 0 0% 100%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Status Colors - ONLY ONE SHADE PER COLOR */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --destructive-subtle: 0 84.2% 98%;
    --success: 160 84.2% 39.4%;
    --success-foreground: 0 0% 100%;
    --success-subtle: 160 84.2% 98%;
    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 100%;
    --warning-subtle: 35 85% 98%;
    --info: 212 85% 60%;
    --info-foreground: 0 0% 100%;
    
    /* UI Element Colors */
    --border: 214.3 31.8% 91.4%;
    --input: 0 0% 100%;
    --ring: 160 84.2% 39.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Chart Color - SINGLE BRAND GREEN ONLY */
    --chart: 160 84.2% 39.4%;
  }

  /* Font Character Styles Only */
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: Inter, system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
  }

  /* Header Background Color Only */
  nav, header {
    background-color: hsl(var(--background));
  }

  /* UNIFIED BUTTON SYSTEM - ONLY BRAND GREEN ALLOWED */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
  
  .btn-primary:hover {
    background-color: hsl(var(--primary));
    opacity: 0.9;
  }
  
  .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
  }
  
  .btn-secondary:hover {
    background-color: hsl(var(--secondary-hover));
  }
}
